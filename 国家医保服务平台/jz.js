/*
 * @LastEditors: Mishi <EMAIL>
 * @Date: 2025-08-21 01:01:23
 * @LastEditTime: 2025-08-21 01:04:50
 * @FilePath: /逆向百例/国家医保服务平台/jz.js
 */
import axios from 'axios';

const response = await axios.post(
  'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital',
  {
    'data': {
      'data': {
        'encData': '3DFBCA4667B978F639BB23B95DCE4CC79C3A9FAE7584B01CC77D5368EF62B4BDCCD20943B4DAE96380B41164D761DE9742C84A985FE3BABC31CB352556BB87C9C1495DB24A29AB6BC3A85AB7FCA00F33C56677481A67C67F739EE2C7D589054DC373615B5DDB33C24C5B31E61CB7643E8B42A188F410D5F4A782ADF1A9AFECBAD0B4E7BB97C60BF8E5275CAFCAFD1E13E384C10195003FD638576645B5EF45EA'
      },
      'appCode': 'T98HPCGN5ZVVQBS8LZQNOAEXVI9GYHKQ',
      'version': '1.0.0',
      'encType': 'SM4',
      'signType': 'SM2',
      'timestamp': **********,
      'signData': 'IqYjfzrcuvax1QGeBxb4mErsCzgVM3mrM2+rMq6k0WdEx1BnLa0S/7AhiTgQpZzqOOXAh2+jI6vnVGpUqFBLOg=='
    }
  },
  {
    headers: {
      'Accept': 'application/json',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Content-Type': 'application/json',
      'Origin': 'https://fuwu.nhsa.gov.cn',
      'Pragma': 'no-cache',
      'Referer': 'https://fuwu.nhsa.gov.cn/nationalHallSt/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36',
      'X-Tingyun': 'c=B|4Nl_NnGbjwY;x=f05e45fe555741e6',
      'channel': 'web',
      'contentType': 'application/x-www-form-urlencoded',
      'dnt': '1',
      'sec-ch-ua': '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-gpc': '1',
      'x-tif-nonce': 'q7RP1Bbm',
      'x-tif-paasid': 'undefined',
      'x-tif-signature': '23e44ee05edb240c01801cadd5b0ed725023fa98c7f3ad6c5954992da233640d',
      'x-tif-timestamp': '**********',
      'Cookie': 'acw_tc=276aeddf17557039737606346e03e14d7763e6d013a2ef57de37a248143f1f; amap_local=340200; yb_header_show=true; yb_header_active=-1; gb_nthl_sessionId=964f027bf0fc4fedbf957cba9b3a1624'
    }
  }
);
console.log(response.data);