import axios from 'axios';
const sm4 = require('sm-crypto').sm4;

const KEY_HEX = '43334145353837334430383431384441'; 

function decryptSM4Response(apiResponse) {
    if (!apiResponse || !apiResponse.data || !apiResponse.data.data || !apiResponse.data.data.encData) {
         console.error('Invalid API response structure for SM4 decryption.');
         return null;
    }

    const encDataHex = apiResponse.data.data.encData;

        const decryptedText = sm4.decrypt(encDataHex, KEY_HEX, {
            mode: 'ecb',
            padding: 'pkcs7', 
            output: 'string'
        });

        if (decryptedText) {
            // 解密后的内容应该是JSON字符串
            const decryptedData = JSON.parse(decryptedText);
            console.log('Decryption successful.');
            return decryptedData;
        } else {
            console.error('Decryption returned empty result.');
            return null;
        }

}

// --- API请求函数 ---
async function fetchApiResponse() {
    try {
        const response = await axios.post(
            'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital',
            {
                'data': {
                    'data': {
                        'encData': '3DFBCA4667B978F639BB23B95DCE4CC79C3A9FAE7584B01CC77D5368EF62B4BDCCD20943B4DAE96380B41164D761DE9742C84A985FE3BABC31CB352556BB87C9C1495DB24A29AB6BC3A85AB7FCA00F33C56677481A67C67F739EE2C7D589054DC373615B5DDB33C24C5B31E61CB7643E8B42A188F410D5F4A782ADF1A9AFECBAD0B4E7BB97C60BF8E5275CAFCAFD1E13E384C10195003FD638576645B5EF45EA'
                    },
                    'appCode': 'T98HPCGN5ZVVQBS8LZQNOAEXVI9GYHKQ',
                    'version': '1.0.0',
                    'encType': 'SM4',
                    'signType': 'SM2',
                    'timestamp': **********,
                    'signData': 'IqYjfzrcuvax1QGeBxb4mErsCzgVM3mrM2+rMq6k0WdEx1BnLa0S/7AhiTgQpZzqOOXAh2+jI6vnVGpUqFBLOg=='
                }
            },
            {
                headers: {
                    'Accept': 'application/json',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Content-Type': 'application/json',
                    'Origin': 'https://fuwu.nhsa.gov.cn',
                    'Pragma': 'no-cache',
                    'Referer': 'https://fuwu.nhsa.gov.cn/nationalHallSt/',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36',
                    'X-Tingyun': 'c=B|4Nl_NnGbjwY;x=f05e45fe555741e6',
                    'channel': 'web',
                    'contentType': 'application/x-www-form-urlencoded',
                    'dnt': '1',
                    'sec-ch-ua': '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-gpc': '1',
                    'x-tif-nonce': 'q7RP1Bbm',
                    'x-tif-paasid': 'undefined',
                    'x-tif-signature': '23e44ee05edb240c01801cadd5b0ed725023fa98c7f3ad6c5954992da233640d',
                    'x-tif-timestamp': '**********',
                    'Cookie': 'acw_tc=276aeddf17557039737606346e03e14d7763e6d013a2ef57de37a248143f1f; amap_local=340200; yb_header_show=true; yb_header_active=-1; gb_nthl_sessionId=964f027bf0fc4fedbf957cba9b3a1624'
                }
            }
        );

        console.log('API请求成功');
        return response.data;
    } catch (error) {
        console.error('API请求失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        throw error;
    }
}

// --- 使用示例 ---
const u = "T98HPCGN5ZVVQBS8LZQNOAEXVI9GYHKQ";
const c = "NMVFVILMKT13GEMD3BKPKCTBOQBPZR2P";

const apiResponse = {
    "code": 0,
    "data": {
        "signData": "OoViRMPmn+rTgvwIWz1qmk3FSkBDXu36eLceaulkiIDnF753Jid3vZcY8Hy9ycjXyS5sQag5Jh+cpzeW/0vwLA==",
        "encType": "SM4",
        "data": {
            // 这里放你的完整 encData
            "encData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
        },
        "signType": "SM2",
        "appCode": "T98HPCGN5ZVVQBS8LZQNOAEXVI9GYHKQ",
        "version": "1.0.0",
        "timestamp": "1755704602173"
    },
    "message": "成功",
    "timestamp": "1755704602",
    "type": "success"
};

// --- 执行解密 ---
const result = decryptSM4Response(apiResponse);
if (result) {
    console.log('Decrypted Data:');
    console.log(JSON.stringify(result, null, 2));
} else {
    console.log('Failed to decrypt.');
}